# GMCadiom Logger Provider

A flexible and extensible logging provider library that supports multiple logging frameworks with a unified interface.

## Features

- **Multiple Provider Support**: Microsoft Logging and Serilog out of the box
- **Easy Integration**: Simple dependency injection setup
- **Fluent Configuration**: Builder pattern for easy configuration
- **Extensible**: Support for custom logger providers
- **Factory Pattern**: Clean provider creation and management
- **Null Object Pattern**: Graceful handling of disabled logging

## Supported Providers

- **Microsoft.Extensions.Logging** - Built-in .NET logging
- **Serilog** - Structured logging library

## Quick Start

### 1. Simplest Usage (Static Helper)

```csharp
using GMCadiomLoggerProvider;

// Create logger with default settings
var logger = GMCadiomLogger.CreateDefault();
logger.Information("Hello, World!");

// Or create specific types
var fileLogger = GMCadiomLogger.CreateFileLogger("logs/app-.log");
var consoleLogger = GMCadiomLogger.CreateConsoleLogger();
var productionLogger = GMCadiomLogger.CreateProductionLogger("MyApp");
```

### 2. Builder Pattern

```csharp
using GMCadiomLoggerProvider;

// Create logger with custom configuration
var logger = GMCadiomLogger.Create()
    .UseSerilog()
    .WithConsoleLogging()
    .WithFileLogging(true, "logs/app-.log")
    .WithMinimumLevel(LogLevel.Information)
    .Build();

logger.Information("Hello, World!");
```

### 3. Dependency Injection Setup

```csharp
using Microsoft.Extensions.DependencyInjection;
using GMCadiomLoggerProvider.Extensions;

var services = new ServiceCollection();

// Add with default configuration
services.AddGMCadiomLogger();

// Or configure specific provider
services.AddGMCadiomSerilogLogger(options =>
{
    options.MinimumLevel = LogLevel.Debug;
    options.LogFilePath = "logs/app-.log";
    options.EnableConsoleLogging = true;
    options.EnableFileLogging = true;
});

var serviceProvider = services.BuildServiceProvider();
var logger = serviceProvider.GetRequiredService<ILoggingProvider>();
```

### 4. ASP.NET Core Integration

```csharp
// Program.cs
using GMCadiomLoggerProvider.Extensions;

var builder = WebApplication.CreateBuilder(args);

// Add GMCadiom Logger
builder.Services.AddGMCadiomSerilogLogger(options =>
{
    options.LogFilePath = "logs/webapp-.log";
    options.RollingInterval = RollingInterval.Day;
    options.LevelOverrides["Microsoft"] = LogLevel.Warning;
    options.LevelOverrides["System"] = LogLevel.Warning;
});

var app = builder.Build();

// Use in controllers or services
public class HomeController : ControllerBase
{
    private readonly ILoggingProvider _logger;
    
    public HomeController(ILoggingProvider logger)
    {
        _logger = logger;
    }
    
    public IActionResult Index()
    {
        _logger.Information("Home page accessed");
        return View();
    }
}
```

## Static Helper Methods

The `GMCadiomLogger` static class provides convenient methods for common scenarios:

```csharp
// Quick defaults
var logger = GMCadiomLogger.CreateDefault();           // Serilog with console
var msLogger = GMCadiomLogger.CreateMicrosoft();       // Microsoft with console + debug
var serilogLogger = GMCadiomLogger.CreateSerilog();    // Serilog with console

// File logging
var fileLogger = GMCadiomLogger.CreateFileLogger("logs/app-.log");

// Console only
var consoleLogger = GMCadiomLogger.CreateConsoleLogger(LoggerProvider.Serilog, LogLevel.Debug);

// Environment-specific
var prodLogger = GMCadiomLogger.CreateProductionLogger("MyApp");     // Optimized for production
var devLogger = GMCadiomLogger.CreateDevelopmentLogger("MyApp");     // Verbose for development

// Disabled logging
var nullLogger = GMCadiomLogger.CreateDisabled();      // No-op logger
```

## Advanced Configuration

### Builder Pattern

```csharp
var logger = new LoggerProviderBuilder()
    .UseSerilog()
    .WithMinimumLevel(LogLevel.Debug)
    .WithConsoleLogging(true)
    .WithFileLogging(true, "logs/app-.log")
    .WithRollingInterval(RollingInterval.Day)
    .WithLevelOverride("Microsoft", LogLevel.Warning)
    .WithLevelOverride("System", LogLevel.Warning)
    .WithStructuredLogging(structured =>
    {
        structured.IncludeTimestamp = true;
        structured.IncludeRequestId = true;
    })
    .Build();
```

### Custom Provider Registration

```csharp
// Define custom provider enum value
public enum CustomLoggerProvider
{
    NLog = 100
}

// Register custom provider
services.AddCustomLoggerProvider(
    (LoggerProvider)CustomLoggerProvider.NLog,
    options => new NLogLoggingProvider(options)
);
```

## Configuration Options

### LoggingOptions Properties

- `Enabled`: Enable/disable logging (default: true)
- `LoggerProvider`: Provider type (Microsoft/Serilog)
- `MinimumLevel`: Minimum log level (default: Information)
- `LogFilePath`: File path for file logging
- `EnableConsoleLogging`: Enable console output (default: true)
- `EnableFileLogging`: Enable file output (default: true)
- `EnableDebugLogging`: Enable debug output (default: true)
- `RollingInterval`: File rolling interval (default: Day)
- `LevelOverrides`: Category-specific log levels
- `StructuredLogging`: Structured logging options

### Log Levels

- `Verbose` / `Trace`
- `Debug`
- `Information`
- `Warning`
- `Error`
- `Fatal`

## Extension Methods

The library provides several extension methods for easy integration:

- `AddGMCadiomLogger()` - Add with default configuration
- `AddGMCadiomMicrosoftLogger()` - Add Microsoft provider
- `AddGMCadiomSerilogLogger()` - Add Serilog provider
- `AddCustomLoggerProvider()` - Register custom provider

## Best Practices

1. **Use Dependency Injection**: Register the logger in your DI container
2. **Configure Log Levels**: Set appropriate minimum levels for production
3. **Use Structured Logging**: Enable structured logging for better searchability
4. **Category Overrides**: Use level overrides to reduce noise from frameworks
5. **File Rolling**: Configure appropriate rolling intervals for file logging

## License

This project is licensed under the MIT License.
