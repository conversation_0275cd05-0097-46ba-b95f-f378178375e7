using Microsoft.Extensions.Logging;

namespace GMCadiomLoggerProvider.Implementation
{
    /// <summary>
    /// File logger provider for Microsoft.Extensions.Logging
    /// </summary>
    internal class FileLoggerProvider : ILoggerProvider
    {
        private readonly string _filePath;
        private readonly RollingInterval _rollingInterval;
        private readonly ConcurrentDictionary<string, FileLogger> _loggers = new();
        private readonly object _lock = new();

        public FileLoggerProvider(string filePath, RollingInterval rollingInterval)
        {
            _filePath = filePath ?? throw new ArgumentNullException(nameof(filePath));
            _rollingInterval = rollingInterval;
        }

        public ILogger CreateLogger(string categoryName)
        {
            return _loggers.GetOrAdd(categoryName, name => new FileLogger(name, _filePath, _rollingInterval, _lock));
        }

        public void Dispose()
        {
            _loggers.Clear();
        }
    }

    /// <summary>
    /// File logger implementation for Microsoft.Extensions.Logging
    /// </summary>
    internal class FileLogger : ILogger
    {
        private readonly string _categoryName;
        private readonly string _filePath;
        private readonly RollingInterval _rollingInterval;
        private readonly object _lock;

        public FileLogger(string categoryName, string filePath, RollingInterval rollingInterval, object lockObject)
        {
            _categoryName = categoryName;
            _filePath = filePath;
            _rollingInterval = rollingInterval;
            _lock = lockObject;
        }

        public IDisposable? BeginScope<TState>(TState state) where TState : notnull => null;

        public bool IsEnabled(Microsoft.Extensions.Logging.LogLevel logLevel) => logLevel != Microsoft.Extensions.Logging.LogLevel.None;

        public void Log<TState>(Microsoft.Extensions.Logging.LogLevel logLevel, EventId eventId, TState state, Exception? exception, Func<TState, Exception?, string> formatter)
        {
            if (!IsEnabled(logLevel))
                return;

            var message = formatter(state, exception);
            var logEntry = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}] [{logLevel}] [{_categoryName}] {message}";

            if (exception != null)
            {
                logEntry += Environment.NewLine + exception.ToString();
            }

            WriteToFile(logEntry);
        }

        private void WriteToFile(string message)
        {
            try
            {
                lock (_lock)
                {
                    var actualFilePath = GetActualFilePath();
                    var directory = Path.GetDirectoryName(actualFilePath);

                    if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                    {
                        Directory.CreateDirectory(directory);
                    }

                    File.AppendAllText(actualFilePath, message + Environment.NewLine);
                }
            }
            catch
            {
                // Silently ignore file writing errors to prevent logging from breaking the application
            }
        }

        private string GetActualFilePath()
        {
            var now = DateTime.Now;
            var basePath = _filePath;

            // Handle rolling file names based on interval
            var suffix = _rollingInterval switch
            {
                RollingInterval.Year => now.ToString("yyyy"),
                RollingInterval.Month => now.ToString("yyyyMM"),
                RollingInterval.Day => now.ToString("yyyyMMdd"),
                RollingInterval.Hour => now.ToString("yyyyMMddHH"),
                RollingInterval.Minute => now.ToString("yyyyMMddHHmm"),
                _ => string.Empty
            };

            if (!string.IsNullOrEmpty(suffix))
            {
                var directory = Path.GetDirectoryName(basePath);
                var fileName = Path.GetFileNameWithoutExtension(basePath);
                var extension = Path.GetExtension(basePath);

                if (string.IsNullOrEmpty(directory))
                    directory = ".";

                return Path.Combine(directory, $"{fileName}{suffix}{extension}");
            }

            return basePath;
        }
    }
}
