﻿namespace GMCadiomLoggerProvider.Abstraction
{
    public interface ILoggingProvider
    {
        void Trace(string message, params object[] args);
        void Debug(string message, params object[] args);
        void Information(string message, params object[] args);
        void Warning(string message, params object[] args);
        void Error(string message, params object[] args);
        void Error(Exception exception, string message, params object[] args);
        void Fatal(string message, params object[] args);
        void Log(LogLevel level, string message, params object[] args);
    }
}
