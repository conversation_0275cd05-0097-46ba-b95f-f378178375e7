using Serilog;
using Serilog.Events;

namespace GMCadiomLoggerProvider.Implementation
{
    internal class SerilogLoggingProvider : ILoggingProvider
    {
        private readonly ILogger _logger;
        private readonly LoggingOptions _options;

        public SerilogLoggingProvider(LoggingOptions options)
        {
            _options = options;
            _logger = CreateSerilogLogger(options);
        }

        public void Debug(string message, params object[] args) =>
            _logger.Debug(message, args);

        public void Error(string message, params object[] args) =>
            _logger.Error(message, args);

        public void Error(Exception exception, string message, params object[] args) =>
            _logger.Error(exception, message, args);

        public void Fatal(string message, params object[] args) =>
            _logger.Fatal(message, args);

        public void Information(string message, params object[] args) =>
            _logger.Information(message, args);

        public void Log(Configuration.LogLevel level, string message, params object[] args) =>
            _logger.Write(ConvertLogLevel(level), message, args);

        public void Trace(string message, params object[] args) =>
            _logger.Verbose(message, args);

        public void Warning(string message, params object[] args) =>
            _logger.Warning(message, args);

        private static ILogger CreateSerilogLogger(LoggingOptions options)
        {
            var loggerConfig = new LoggerConfiguration()
                .MinimumLevel.Is(ConvertLogLevel(options.MinimumLevel));

            // Apply level overrides
            foreach (var levelOverride in options.LevelOverrides)
            {
                loggerConfig.MinimumLevel.Override(levelOverride.Key, ConvertLogLevel(levelOverride.Value));
            }

            // Configure console logging
            if (options.EnableConsoleLogging)
            {
                loggerConfig.WriteTo.Console(
                    restrictedToMinimumLevel: ConvertLogLevel(options.MinimumLevel),
                    outputTemplate: "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj}{NewLine}{Exception}");
            }

            // Configure file logging
            if (options.EnableFileLogging && !string.IsNullOrEmpty(options.LogFilePath))
            {
                var rollingInterval = ConvertRollingInterval(options.RollingInterval);
                loggerConfig.WriteTo.File(
                    path: options.LogFilePath,
                    restrictedToMinimumLevel: ConvertLogLevel(options.MinimumLevel),
                    rollingInterval: rollingInterval,
                    outputTemplate: "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} {Level:u3}] {Message:lj}{NewLine}{Exception}");
            }

            // Configure structured logging enrichers
            if (options.StructuredLogging.IncludeTimestamp)
            {
                loggerConfig.Enrich.WithProperty("Timestamp", DateTime.UtcNow);
            }

            return loggerConfig.CreateLogger();
        }

        private static LogEventLevel ConvertLogLevel(LogLevel level) => level switch
        {
            LogLevel.Verbose => LogEventLevel.Verbose,
            LogLevel.Debug => LogEventLevel.Debug,
            LogLevel.Information => LogEventLevel.Information,
            LogLevel.Warning => LogEventLevel.Warning,
            LogLevel.Error => LogEventLevel.Error,
            LogLevel.Fatal => LogEventLevel.Fatal,
            _ => LogEventLevel.Information
        };

        private static Serilog.RollingInterval ConvertRollingInterval(Configuration.RollingInterval interval) => interval switch
        {
            Configuration.RollingInterval.Infinite => Serilog.RollingInterval.Infinite,
            Configuration.RollingInterval.Year => Serilog.RollingInterval.Year,
            Configuration.RollingInterval.Month => Serilog.RollingInterval.Month,
            Configuration.RollingInterval.Day => Serilog.RollingInterval.Day,
            Configuration.RollingInterval.Hour => Serilog.RollingInterval.Hour,
            Configuration.RollingInterval.Minute => Serilog.RollingInterval.Minute,
            _ => Serilog.RollingInterval.Day
        };
    }
}
