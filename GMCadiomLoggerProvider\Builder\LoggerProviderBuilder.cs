namespace GMCadiomLoggerProvider.Builder
{
    /// <summary>
    /// Builder class for fluent configuration of logger providers
    /// </summary>
    public class LoggerProviderBuilder
    {
        private readonly LoggingOptions _options;
        private readonly ILoggerProviderFactory _factory;

        public LoggerProviderBuilder(ILoggerProviderFactory? factory = null)
        {
            _options = new LoggingOptions();
            _factory = factory ?? new LoggerProviderFactory();
        }

        /// <summary>
        /// Sets the logger provider type
        /// </summary>
        /// <param name="providerType">The provider type to use</param>
        /// <returns>The builder instance for chaining</returns>
        public LoggerProviderBuilder UseProvider(LoggerProvider providerType)
        {
            _options.LoggerProvider = providerType;
            return this;
        }

        /// <summary>
        /// Uses Microsoft Logging provider
        /// </summary>
        /// <returns>The builder instance for chaining</returns>
        public LoggerProviderBuilder UseMicrosoft()
        {
            return UseProvider(LoggerProvider.Microsoft);
        }

        /// <summary>
        /// Uses Serilog provider
        /// </summary>
        /// <returns>The builder instance for chaining</returns>
        public LoggerProviderBuilder UseSerilog()
        {
            return UseProvider(LoggerProvider.Serilog);
        }

        /// <summary>
        /// Sets the minimum log level
        /// </summary>
        /// <param name="level">The minimum log level</param>
        /// <returns>The builder instance for chaining</returns>
        public LoggerProviderBuilder WithMinimumLevel(LogLevel level)
        {
            _options.MinimumLevel = level;
            return this;
        }

        /// <summary>
        /// Enables or disables console logging
        /// </summary>
        /// <param name="enabled">Whether to enable console logging</param>
        /// <returns>The builder instance for chaining</returns>
        public LoggerProviderBuilder WithConsoleLogging(bool enabled = true)
        {
            _options.EnableConsoleLogging = enabled;
            return this;
        }

        /// <summary>
        /// Enables or disables file logging
        /// </summary>
        /// <param name="enabled">Whether to enable file logging</param>
        /// <param name="filePath">Optional file path for logging</param>
        /// <returns>The builder instance for chaining</returns>
        public LoggerProviderBuilder WithFileLogging(bool enabled = true, string? filePath = null)
        {
            _options.EnableFileLogging = enabled;
            if (!string.IsNullOrEmpty(filePath))
            {
                _options.LogFilePath = filePath;
            }
            return this;
        }

        /// <summary>
        /// Enables or disables debug logging
        /// </summary>
        /// <param name="enabled">Whether to enable debug logging</param>
        /// <returns>The builder instance for chaining</returns>
        public LoggerProviderBuilder WithDebugLogging(bool enabled = true)
        {
            _options.EnableDebugLogging = enabled;
            return this;
        }

        /// <summary>
        /// Sets the rolling interval for file logging
        /// </summary>
        /// <param name="interval">The rolling interval</param>
        /// <returns>The builder instance for chaining</returns>
        public LoggerProviderBuilder WithRollingInterval(RollingInterval interval)
        {
            _options.RollingInterval = interval;
            return this;
        }

        /// <summary>
        /// Adds a log level override for a specific category
        /// </summary>
        /// <param name="category">The category name</param>
        /// <param name="level">The log level for this category</param>
        /// <returns>The builder instance for chaining</returns>
        public LoggerProviderBuilder WithLevelOverride(string category, LogLevel level)
        {
            _options.LevelOverrides[category] = level;
            return this;
        }

        /// <summary>
        /// Configures structured logging options
        /// </summary>
        /// <param name="configure">Action to configure structured logging</param>
        /// <returns>The builder instance for chaining</returns>
        public LoggerProviderBuilder WithStructuredLogging(Action<StructuredLoggingOptions> configure)
        {
            configure(_options.StructuredLogging);
            return this;
        }

        /// <summary>
        /// Enables or disables the logger
        /// </summary>
        /// <param name="enabled">Whether to enable the logger</param>
        /// <returns>The builder instance for chaining</returns>
        public LoggerProviderBuilder WithEnabled(bool enabled = true)
        {
            _options.Enabled = enabled;
            return this;
        }

        /// <summary>
        /// Applies custom configuration to the options
        /// </summary>
        /// <param name="configure">Action to configure the options</param>
        /// <returns>The builder instance for chaining</returns>
        public LoggerProviderBuilder Configure(Action<LoggingOptions> configure)
        {
            configure(_options);
            return this;
        }

        /// <summary>
        /// Builds and returns the configured logger provider
        /// </summary>
        /// <returns>The configured ILoggingProvider instance</returns>
        public ILoggingProvider Build()
        {
            return _factory.CreateProvider(_options);
        }

        /// <summary>
        /// Gets the current configuration options
        /// </summary>
        /// <returns>The current LoggingOptions</returns>
        public LoggingOptions GetOptions()
        {
            return _options;
        }
    }
}
