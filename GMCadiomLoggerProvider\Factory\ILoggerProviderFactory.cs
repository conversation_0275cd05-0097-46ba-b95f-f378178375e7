namespace GMCadiomLoggerProvider.Factory
{
    /// <summary>
    /// Factory interface for creating logger provider instances
    /// </summary>
    public interface ILoggerProviderFactory
    {
        /// <summary>
        /// Creates a logger provider instance based on the specified provider type
        /// </summary>
        /// <param name="providerType">The type of logger provider to create</param>
        /// <param name="options">Configuration options for the logger</param>
        /// <returns>An instance of ILoggingProvider</returns>
        ILoggingProvider CreateProvider(LoggerProvider providerType, LoggingOptions options);

        /// <summary>
        /// Creates a logger provider instance based on the options configuration
        /// </summary>
        /// <param name="options">Configuration options containing the provider type</param>
        /// <returns>An instance of ILoggingProvider</returns>
        ILoggingProvider CreateProvider(LoggingOptions options);

        /// <summary>
        /// Registers a custom logger provider factory function
        /// </summary>
        /// <param name="providerType">The provider type to register</param>
        /// <param name="factory">Factory function to create the provider</param>
        void RegisterCustomProvider(LoggerProvider providerType, Func<LoggingOptions, ILoggingProvider> factory);

        /// <summary>
        /// Gets all available provider types
        /// </summary>
        /// <returns>Collection of available provider types</returns>
        IEnumerable<LoggerProvider> GetAvailableProviders();
    }
}
