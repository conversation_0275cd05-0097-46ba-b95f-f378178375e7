﻿using Microsoft.Extensions.Logging;

namespace GMCadiomLoggerProvider.Implementation
{
    internal class MicrosoftLoggingProvider : ILoggingProvider, IDisposable
    {
        private readonly ILogger _logger;
        private readonly LoggingOptions _options;
        private readonly ILoggerFactory _loggerFactory;

        public MicrosoftLoggingProvider(LoggingOptions options)
        {
            _options = options ?? throw new ArgumentNullException(nameof(options));
            _loggerFactory = CreateLoggerFactory(options);
            _logger = _loggerFactory.CreateLogger("GMCadiomLogger");
        }

        public void Debug(string message, params object[] args) =>
            _logger.LogDebug(message, args);

        public void Error(string message, params object[] args) =>
            _logger.LogError(message, args);

        public void Error(Exception exception, string message, params object[] args) =>
            _logger.LogError(exception, message, args);

        public void Fatal(string message, params object[] args) =>
            _logger.LogCritical(message, args);

        public void Information(string message, params object[] args) =>
            _logger.LogInformation(message, args);

        public void Log(Configuration.LogLevel level, string message, params object[] args) =>
            _logger.Log((Microsoft.Extensions.Logging.LogLevel)level, message, args);

        public void Trace(string message, params object[] args) =>
            _logger.LogTrace(message, args);

        public void Warning(string message, params object[] args) =>
            _logger.LogWarning(message, args);

        private static ILoggerFactory CreateLoggerFactory(LoggingOptions options)
        {
            return LoggerFactory.Create(builder =>
            {
                // Set minimum level
                builder.SetMinimumLevel(ConvertLogLevel(options.MinimumLevel));

                // Apply level overrides
                foreach (var levelOverride in options.LevelOverrides)
                {
                    builder.AddFilter(levelOverride.Key, ConvertLogLevel(levelOverride.Value));
                }

                // Configure console logging
                if (options.EnableConsoleLogging)
                {
                    builder.AddConsole();
                }

                // Configure debug logging
                if (options.EnableDebugLogging)
                {
                    builder.AddDebug();
                }

                // Configure file logging with custom provider
                if (options.EnableFileLogging && !string.IsNullOrEmpty(options.LogFilePath))
                {
                    builder.AddProvider(new FileLoggerProvider(options.LogFilePath, options.RollingInterval));
                }

                // Configure structured logging enrichment
                if (options.StructuredLogging.IncludeTimestamp ||
                    options.StructuredLogging.IncludeRequestId ||
                    options.StructuredLogging.IncludeUserId ||
                    options.StructuredLogging.IncludeConnectionId)
                {
                    builder.AddFilter("StructuredLogging", Microsoft.Extensions.Logging.LogLevel.Trace);
                }
            });
        }

        private static Microsoft.Extensions.Logging.LogLevel ConvertLogLevel(Configuration.LogLevel level) => level switch
        {
            Configuration.LogLevel.Verbose => Microsoft.Extensions.Logging.LogLevel.Trace,
            Configuration.LogLevel.Debug => Microsoft.Extensions.Logging.LogLevel.Debug,
            Configuration.LogLevel.Information => Microsoft.Extensions.Logging.LogLevel.Information,
            Configuration.LogLevel.Warning => Microsoft.Extensions.Logging.LogLevel.Warning,
            Configuration.LogLevel.Error => Microsoft.Extensions.Logging.LogLevel.Error,
            Configuration.LogLevel.Fatal => Microsoft.Extensions.Logging.LogLevel.Critical,
            _ => Microsoft.Extensions.Logging.LogLevel.Information
        };

        public void Dispose()
        {
            _loggerFactory?.Dispose();
        }
    }
}
